import { useState, useLayoutEffect, useRef } from 'react';
import { FiCopy, FiRepeat } from 'react-icons/fi';
import { MessageTurn } from './ChatHistory';
import { EditableMessage } from './Message'; 


import { Button } from "@/components/ui/button";
import { cn } from "@/src/background/util";



interface MessagesProps {
  turns?: MessageTurn[];
  onReload?: () => void;
  settingsMode?: boolean;
  onEditTurn: (index: number, newContent: string) => void;
}

export const Messages: React.FC<MessagesProps> = ({
  turns = [], onReload = () => {}, settingsMode = false, onEditTurn
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number>(-1);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editText, setEditText] = useState<string>('');


  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);



  useLayoutEffect(() => {
    const container = containerRef.current;
    if (container) {
      const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
      const isNearBottom = scrollBottom < 200;
      if (isNearBottom) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [turns]);

  const copyMessage = (text: string) => {
    navigator.clipboard.writeText(text)
  };



  const startEdit = (index: number, currentContent: string) => { setEditingIndex(index); setEditText(currentContent); };
  const cancelEdit = () => { setEditingIndex(null); setEditText(''); };
  const saveEdit = () => { if (editingIndex !== null && editText.trim()) { onEditTurn(editingIndex, editText); } cancelEdit(); };

  return (
    <div
      ref={containerRef}
      id="messages"
      className={cn(
        "flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2",
        "no-scrollbar"
      )}
      style={{
        background: 'var(--bg)',
        opacity: settingsMode ? 0 : 1,
      }}
    >
      {turns.map(
        (turn, i) => turn && (
          (<div
            key={turn.timestamp || `turn_${i}`}
            className={cn(
              "flex items-start w-full relative group",
              "border-b border-border/30 last:border-b-0",
              turn.role === 'user' ? 'justify-end' : 'justify-start'
            )}
            onMouseEnter={() => setHoveredIndex(i)}
            onMouseLeave={() => setHoveredIndex(-1)}
          >
            {turn.role === 'assistant' && (
              <div
                className={cn(
                  "flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",
                  hoveredIndex === i ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
                )}
              >
                {editingIndex !== i && (
                  <Button aria-label="Copy" variant="message-action" size="xs" onClick={() => copyMessage(turn.rawContent)} title="Copy message">
                    <FiCopy className="text-[var(--text)]" />
                  </Button>
                )}

                {i === turns.length - 1 && (
                  <Button aria-label="Reload" variant="message-action" size="xs" onClick={onReload} title="Reload last prompt">
                    <FiRepeat className="text-[var(--text)]" />
                  </Button>
                )}
              </div>
            )}
            <EditableMessage
              turn={turn}
              index={i}
              isEditing={editingIndex === i}
              editText={editText}
              onStartEdit={startEdit}
              onSetEditText={setEditText}
              onSaveEdit={saveEdit}
              onCancelEdit={cancelEdit} />
            {turn.role === 'user' && (
              (<div
                 className={cn(
                    "flex flex-row items-center space-x-1 ml-2 transition-opacity duration-100",
                    hoveredIndex === i ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
                  )}
              >
                {editingIndex !== i && (
                  <Button aria-label="Copy" variant="message-action" size="xs" onClick={() => copyMessage(turn.rawContent)} title="Copy message">
                    <FiCopy className="text-[var(--text)]" />
                  </Button>
                )}
              </div>)
            )}
          </div>)
        )
      )}
      <div ref={messagesEndRef} style={{ height: '1px' }} />
    </div>
  );
};